using UnityEngine;
using Chronos;
using BTR.Projectiles;

namespace BTR.Projectiles
{
    /// <summary>
    /// Component responsible for projectile lifecycle management.
    /// Migrated from ProjectileStateBased lifetime and distance tracking system.
    /// </summary>
    public class ProjectileLifecycle : MonoBehaviour, IProjectileLifecycle
    {
        #region Private Fields
        private ProjectileEntity projectile;
        private bool isInitialized = false;

        // Lifecycle configuration
        [SerializeField] private float lifetime = 5f;
        [SerializeField] private float maxDistance = 100f;
        [SerializeField] private bool isLifetimePaused = false;
        [SerializeField] private bool useTimeBasedDestroy = true;
        [SerializeField] private bool useDistanceBasedDestroy = true;
        [SerializeField] private bool returnToPool = true;
        [SerializeField] private bool autoDestroy = true;

        // Cached components
        private Timeline timeline;
        private Transform cachedTransform;

        // Lifecycle state
        private float currentLifetime;
        private float timeAlive = 0f;
        private Vector3 initialPosition;
        private Vector3 lastPosition;
        private float creationTime;
        private float distanceTraveled = 0f;
        private bool isDestroyed = false;
        private bool destructionRequested = false;

        // Performance optimization
        private float lifetimeCheckInterval = 0.1f;
        private float lastLifetimeCheck = 0f;
        #endregion

        #region IProjectileLifecycle Implementation
        public void Initialize(ProjectileEntity projectile)
        {
            this.projectile = projectile;

            // Cache components
            timeline = GetComponent<Timeline>();
            cachedTransform = transform;

            // Initialize state
            currentLifetime = lifetime;
            timeAlive = 0f;
            initialPosition = cachedTransform.position;
            lastPosition = initialPosition;
            creationTime = Time.time;
            distanceTraveled = 0f;
            isDestroyed = false;
            destructionRequested = false;
            lastLifetimeCheck = 0f;

            isInitialized = true;

            Debug.Log($"[ProjectileLifecycle] Initialized for projectile {projectile.EntityID} with lifetime {lifetime}");
        }

        public void UpdateLifecycle(float deltaTime)
        {
            if (!isInitialized || isLifetimePaused || isDestroyed) return;

            // Get time scale from timeline
            float timeScale = timeline != null ? timeline.timeScale : 1f;
            float scaledDeltaTime = deltaTime * Mathf.Abs(timeScale);

            // Update time alive
            timeAlive += scaledDeltaTime;

            // Update distance tracking
            UpdateDistanceTracking();

            // Update remaining lifetime (only if time-based destruction is enabled)
            if (useTimeBasedDestroy)
            {
                currentLifetime -= scaledDeltaTime;
            }

            // Check for destruction conditions (with performance optimization)
            if (Time.time - lastLifetimeCheck >= lifetimeCheckInterval)
            {
                lastLifetimeCheck = Time.time;

                if (ShouldDestroy())
                {
                    RequestDestruction();
                }
            }
        }

        public bool ShouldDestroy()
        {
            if (!isInitialized || isDestroyed) return false;

            // Check if destruction was already requested
            if (destructionRequested) return true;

            // Check lifetime (only if time-based destruction is enabled)
            if (useTimeBasedDestroy && currentLifetime <= 0f)
            {
                Debug.Log($"[ProjectileLifecycle] Projectile should destroy - lifetime expired ({timeAlive:F2}s)");
                return true;
            }

            // Check max distance (only if distance-based destruction is enabled)
            if (useDistanceBasedDestroy && maxDistance > 0f && distanceTraveled >= maxDistance)
            {
                Debug.Log($"[ProjectileLifecycle] Projectile should destroy - max distance reached ({distanceTraveled:F2}m)");
                return true;
            }

            // Check if projectile is out of bounds (additional safety check)
            if (IsOutOfBounds())
            {
                Debug.Log($"[ProjectileLifecycle] Projectile should destroy - out of bounds");
                return true;
            }

            return false;
        }

        public void OnDestroy()
        {
            if (!isDestroyed)
            {
                PerformDestruction();
            }
        }

        public void SetLifetime(float lifetime)
        {
            this.lifetime = lifetime;
            this.currentLifetime = lifetime;
            Debug.Log($"[ProjectileLifecycle] Lifetime set to {lifetime}");
        }

        public float GetRemainingLifetime()
        {
            return currentLifetime;
        }

        public float GetTotalLifetime()
        {
            return lifetime;
        }

        public float GetTimeAlive()
        {
            return timeAlive;
        }

        public void SetLifetimePaused(bool paused)
        {
            isLifetimePaused = paused;
            Debug.Log($"[ProjectileLifecycle] Lifetime {(paused ? "paused" : "resumed")}");
        }

        public bool IsLifetimePaused()
        {
            return isLifetimePaused;
        }

        public void ExtendLifetime(float extension)
        {
            currentLifetime += extension;
            lifetime += extension;
            Debug.Log($"[ProjectileLifecycle] Lifetime extended by {extension} seconds");
        }

        public void SetMaxDistance(float maxDistance)
        {
            this.maxDistance = maxDistance;
            Debug.Log($"[ProjectileLifecycle] Max distance set to {maxDistance}");
        }

        public float GetMaxDistance()
        {
            return maxDistance;
        }

        public void ForceDestroy(bool hitTarget = false)
        {
            currentLifetime = 0f;
            Debug.Log($"[ProjectileLifecycle] Force destroy called - hit target: {hitTarget}");
        }

        public void ResetForPool()
        {
            // Reset all state
            timeAlive = 0f;
            currentLifetime = lifetime;
            initialPosition = cachedTransform != null ? cachedTransform.position : transform.position;
            lastPosition = initialPosition;
            creationTime = Time.time;
            distanceTraveled = 0f;
            isLifetimePaused = false;
            isDestroyed = false;
            destructionRequested = false;
            lastLifetimeCheck = 0f;

            Debug.Log($"[ProjectileLifecycle] Reset for pool");
        }

        public float GetDistanceTraveled()
        {
            return distanceTraveled;
        }

        public bool IsDestroyed()
        {
            return isDestroyed;
        }

        public void RequestDestruction()
        {
            if (!destructionRequested && !isDestroyed)
            {
                destructionRequested = true;
                Debug.Log($"[ProjectileLifecycle] Destruction requested after {timeAlive:F2}s, distance: {distanceTraveled:F2}m");
            }
        }
        #endregion

        #region Private Methods
        private void UpdateDistanceTracking()
        {
            if (cachedTransform == null) return;

            Vector3 currentPosition = cachedTransform.position;
            distanceTraveled += Vector3.Distance(lastPosition, currentPosition);
            lastPosition = currentPosition;
        }

        private bool IsOutOfBounds()
        {
            // Define reasonable bounds for projectiles (can be configured)
            float maxBounds = 10000f; // 10km from origin
            Vector3 position = cachedTransform != null ? cachedTransform.position : transform.position;

            return position.magnitude > maxBounds;
        }

        private void PerformDestruction()
        {
            if (isDestroyed) return;

            isDestroyed = true;

            Debug.Log($"[ProjectileLifecycle] Projectile destroyed after {timeAlive:F2}s, distance: {distanceTraveled:F2}m");

            if (returnToPool && projectile != null)
            {
                // Return to pool through ProjectileManager
                ReturnToPool();
            }
            else if (autoDestroy)
            {
                // Destroy the game object
                if (gameObject != null)
                {
                    Destroy(gameObject);
                }
            }
        }

        private void ReturnToPool()
        {
            try
            {
                if (ProjectileManager.Instance != null && projectile != null)
                {
                    ProjectileManager.Instance.ReturnProjectileToPool(projectile);
                }
                else
                {
                    // Fallback: destroy if pool return fails
                    if (gameObject != null)
                    {
                        Destroy(gameObject);
                    }
                }
            }
            catch (System.Exception e)
            {
                Debug.LogError($"[ProjectileLifecycle] Error returning to pool: {e.Message}");

                // Fallback: destroy the object
                if (gameObject != null)
                {
                    Destroy(gameObject);
                }
            }
        }
        #endregion

        #region Unity Lifecycle
        private void Start()
        {
            if (isInitialized)
            {
                initialPosition = transform.position;
                creationTime = Time.time;
            }
        }
        #endregion

        #region Debug Methods
        private void OnDrawGizmosSelected()
        {
            if (maxDistance > 0f)
            {
                Gizmos.color = Color.yellow;
                Gizmos.DrawWireSphere(initialPosition, maxDistance);
            }
        }
        #endregion
    }
}
