%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 54247856159ee8c4a8e8b8760ba0db07, type: 3}
  m_Name: DodecaESConfiguration
  m_EditorClassIdentifier:
  showCoreSettings: 1
  showLayerSettings: 0
  showCombatSettings: 1
  showProjectileSettings: 1
  showExplosionSettings: 0
  showShieldSettings: 0
  showReferences: 0
  showEffects: 0
  showPhaseSettings: 0
  maxHealth: 100
  moveSpeed: 5
  isVulnerable: 1
  clockKey: Enemy
  obstacleLayerMask:
    serializedVersion: 2
    m_Bits: 0
  enemyLayerMask:
    serializedVersion: 2
    m_Bits: 0
  shieldableLayerMask:
    serializedVersion: 2
    m_Bits: 0
  minPlayerDistance: 5
  maxPlayerDistance: 15
  hitsToKillPart: 3
  attackRange: 300
  attackCooldown: 1
  damageAmount: 10
  projectilePrefab: {fileID: 0}
  projectileSpawnPoint: {fileID: 0}
  hitSound:
    Guid:
      Data1: 0
      Data2: 0
      Data3: 0
      Data4: 0
    Path:
  deathSound:
    Guid:
      Data1: 0
      Data2: 0
      Data3: 0
      Data4: 0
    Path:
  hitEffect: {fileID: 0}
  deathEffect: {fileID: 0}
  projectileSpeed: 70
  projectileLifetime: 10
  projectileScale: 1
  projectileDamage: 10
  enableHoming: 1
  projectileAccuracy: 0.8
  explosionRadius: 3
  explosionDamage: 50
  explosionTriggerDistance: 0.5
  explosionEffect: {fileID: 0}
  explosionSound:
    Guid:
      Data1: 0
      Data2: 0
      Data3: 0
      Data4: 0
    Path:
  shieldHealth: 50
  shieldRegenRate: 10
  shieldRegenDelay: 3
  shieldEffect: {fileID: 0}
  shieldBreakEffect: {fileID: 0}
  shieldRegenEffect: {fileID: 0}
  phases: []
