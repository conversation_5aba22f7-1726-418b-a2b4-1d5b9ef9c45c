using UnityEngine;
using Chronos;
using BTR.Projectiles;

namespace BTR.Projectiles
{
    /// <summary>
    /// Component responsible for projectile movement logic.
    /// Migrated from ProjectileStateBased movement system with job system integration.
    /// </summary>
    public class ProjectileMovement : MonoBehaviour, IProjectileMovement
    {
        #region Private Fields
        private ProjectileEntity projectile;
        private bool isInitialized = false;

        // Movement configuration
        [SerializeField] private float bulletSpeed = 25f;
        [SerializeField] private float turnRate = 5f;
        [SerializeField] private bool homing = false;
        [SerializeField] private Transform currentTarget;
        [SerializeField] private float maxTurnRate = 360f;
        [SerializeField] private float homingStrength = 1f;
        [SerializeField] private float rotateSpeed = 180f;

        // Physics and tracking
        private Rigidbody rb;
        private Timeline timeline;
        private float distanceTraveled = 0f;
        private Vector3 lastPosition;
        private Vector3 lastTargetDirection;
        private Vector3 lastSteeringForce;
        private Vector3 currentVelocity;
        private Vector3 predictedPosition;

        // Job system integration
        private ProjectileJobSystem jobSystem;
        private int projectileIndex = -1;
        private bool useJobSystem = true;

        // Performance optimization
        private Transform cachedTransform;
        private bool hasValidTarget = false;
        private float timeInCurrentMode = 0f;

        // Steering configuration
        private float arrivalDistance = 2f;
        private float steeringForce = 1f;
        private float accuracy = 0.8f;
        #endregion

        #region IProjectileMovement Implementation
        public void Initialize(ProjectileEntity projectile)
        {
            this.projectile = projectile;

            // Cache components
            rb = GetComponent<Rigidbody>();
            timeline = GetComponent<Timeline>();
            cachedTransform = transform;
            lastPosition = cachedTransform.position;

            // Initialize physics
            if (rb != null)
            {
                rb.isKinematic = false;
                rb.useGravity = false;
                rb.linearDamping = 0f;
                rb.angularDamping = 0f;
            }

            // Initialize job system integration
            if (useJobSystem && ProjectileManager.Instance != null)
            {
                jobSystem = ProjectileManager.Instance.GetProjectileJobSystem();
                if (jobSystem != null)
                {
                    projectileIndex = projectile.ProjectileIndex;
                }
            }

            // Initialize movement state
            lastTargetDirection = cachedTransform.forward;
            currentVelocity = lastTargetDirection * bulletSpeed;
            timeInCurrentMode = 0f;

            isInitialized = true;
            Debug.Log($"[ProjectileMovement] Initialized for projectile {projectile.EntityID} - JobSystem: {jobSystem != null}, Index: {projectileIndex}");
        }

        public void UpdateMovement(float deltaTime)
        {
            if (!isInitialized || rb == null) return;

            // Get time scale from timeline
            float timeScale = timeline != null ? timeline.timeScale : 1f;
            float scaledDeltaTime = deltaTime * Mathf.Abs(timeScale);

            // Update distance tracking
            Vector3 currentPosition = cachedTransform.position;
            distanceTraveled += Vector3.Distance(lastPosition, currentPosition);
            lastPosition = currentPosition;

            // Update time in current mode
            timeInCurrentMode += scaledDeltaTime;

            if (useJobSystem && jobSystem != null && projectileIndex >= 0)
            {
                // Use job system for movement
                UpdateMovementWithJobSystem(timeScale);
            }
            else
            {
                // Fallback to direct movement
                UpdateMovementDirect(scaledDeltaTime);
            }
        }

        public Vector3 GetCurrentVelocity()
        {
            return rb != null ? rb.linearVelocity : currentVelocity;
        }

        public bool IsMovementComplete()
        {
            // Movement is complete if we've reached the target (for homing projectiles)
            if (homing && currentTarget != null)
            {
                float distanceToTarget = Vector3.Distance(cachedTransform.position, currentTarget.position);
                return distanceToTarget <= arrivalDistance;
            }
            return false;
        }

        public void EnableHoming(bool enable)
        {
            homing = enable;
            hasValidTarget = enable && currentTarget != null;

            // Update job system if available
            if (useJobSystem && jobSystem != null && projectileIndex >= 0)
            {
                UpdateJobSystemData();
            }

            Debug.Log($"[ProjectileMovement] Homing {(enable ? "enabled" : "disabled")}");
        }

        public void SetTarget(Transform target)
        {
            currentTarget = target;
            hasValidTarget = homing && target != null;

            if (hasValidTarget)
            {
                lastTargetDirection = (target.position - cachedTransform.position).normalized;
                predictedPosition = target.position;
            }

            // Update job system if available
            if (useJobSystem && jobSystem != null && projectileIndex >= 0)
            {
                UpdateJobSystemData();
            }

            Debug.Log($"[ProjectileMovement] Target set to {(target != null ? target.name : "None")}");
        }

        public Transform GetTarget()
        {
            return currentTarget;
        }

        public void SetSpeed(float speed)
        {
            bulletSpeed = speed;

            // Update current velocity magnitude if moving
            if (currentVelocity.magnitude > 0)
            {
                currentVelocity = currentVelocity.normalized * speed;
            }

            // Update job system if available
            if (useJobSystem && jobSystem != null && projectileIndex >= 0)
            {
                UpdateJobSystemData();
            }

            Debug.Log($"[ProjectileMovement] Speed set to {speed}");
        }

        public float GetSpeed()
        {
            return bulletSpeed;
        }

        public float GetDistanceTraveled()
        {
            return distanceTraveled;
        }

        public bool IsHoming()
        {
            return homing && currentTarget != null;
        }

        public void SetInitialDirection(Vector3 direction)
        {
            lastTargetDirection = direction.normalized;
            currentVelocity = direction.normalized * bulletSpeed;

            if (rb != null)
            {
                rb.linearVelocity = currentVelocity;
            }
        }
        #endregion

        #region Private Movement Methods
        private void UpdateMovementWithJobSystem(float timeScale)
        {
            if (jobSystem == null || projectileIndex < 0) return;

            // Update job system with current data
            Vector3 targetPosition = currentTarget != null ? currentTarget.position : cachedTransform.position + cachedTransform.forward * 100f;

            jobSystem.UpdateProjectileMovementData(
                projectileIndex,
                cachedTransform.position,
                cachedTransform.rotation,
                rb.linearVelocity,
                targetPosition,
                homing && currentTarget != null,
                rotateSpeed,
                bulletSpeed,
                timeScale,
                projectile.Lifetime
            );

            // Apply results from job system
            Vector3 newPosition = jobSystem.GetProjectilePosition(projectileIndex);
            Quaternion newRotation = jobSystem.GetProjectileRotation(projectileIndex);
            Vector3 newVelocity = jobSystem.GetProjectileVelocity(projectileIndex);

            ApplyJobResults(newPosition, newRotation, newVelocity);
        }

        private void UpdateMovementDirect(float deltaTime)
        {
            Vector3 targetVelocity = CalculateTargetVelocity(deltaTime);

            // Apply movement
            rb.linearVelocity = targetVelocity;
            currentVelocity = targetVelocity;

            // Update rotation if homing
            if (homing && hasValidTarget)
            {
                UpdateRotation(deltaTime);
            }
        }

        private Vector3 CalculateTargetVelocity(float deltaTime)
        {
            Vector3 baseVelocity = cachedTransform.forward * bulletSpeed;

            if (!homing || currentTarget == null)
            {
                hasValidTarget = false;
                return baseVelocity;
            }

            hasValidTarget = true;

            // Calculate direction to target with prediction
            Vector3 targetPosition = PredictTargetPosition();
            Vector3 directionToTarget = (targetPosition - cachedTransform.position).normalized;

            // Calculate steering force
            Vector3 desiredVelocity = directionToTarget * bulletSpeed;
            Vector3 steeringForceVector = (desiredVelocity - currentVelocity) * steeringForce;

            // Apply proximity factor for smoother arrival
            float distanceToTarget = Vector3.Distance(cachedTransform.position, targetPosition);
            float proximityFactor = Mathf.Clamp01(distanceToTarget / (arrivalDistance * 2f));
            steeringForceVector *= proximityFactor;

            // Smooth the steering to prevent jittery movement
            steeringForceVector = Vector3.Lerp(lastSteeringForce, steeringForceVector, deltaTime * turnRate);
            lastSteeringForce = steeringForceVector;

            // Apply steering to current velocity
            currentVelocity += steeringForceVector * deltaTime;

            // Maintain speed
            currentVelocity = currentVelocity.normalized * bulletSpeed;

            return currentVelocity;
        }

        private Vector3 PredictTargetPosition()
        {
            if (currentTarget == null) return Vector3.zero;

            // Simple prediction based on target's movement
            Vector3 targetPosition = currentTarget.position;

            // If target has a rigidbody, predict its movement
            Rigidbody targetRb = currentTarget.GetComponent<Rigidbody>();
            if (targetRb != null)
            {
                float timeToTarget = Vector3.Distance(cachedTransform.position, targetPosition) / bulletSpeed;
                targetPosition += targetRb.linearVelocity * timeToTarget * accuracy;
            }

            return targetPosition;
        }

        private void UpdateRotation(float deltaTime)
        {
            if (currentTarget == null) return;

            Vector3 targetDirection = (PredictTargetPosition() - cachedTransform.position).normalized;

            // Smooth rotation towards target
            Vector3 smoothedDirection = Vector3.Slerp(lastTargetDirection, targetDirection, deltaTime * turnRate);
            lastTargetDirection = smoothedDirection;

            // Apply rotation with max turn rate limit
            float maxRotationThisFrame = maxTurnRate * deltaTime;
            Vector3 newForward = Vector3.RotateTowards(cachedTransform.forward, smoothedDirection,
                                                      Mathf.Deg2Rad * maxRotationThisFrame, 0f);

            cachedTransform.rotation = Quaternion.LookRotation(newForward);
        }

        private void ApplyJobResults(Vector3 position, Quaternion rotation, Vector3 velocity)
        {
            try
            {
                // Validate results
                if (float.IsNaN(position.x) || float.IsNaN(position.y) || float.IsNaN(position.z) ||
                    float.IsInfinity(position.x) || float.IsInfinity(position.y) || float.IsInfinity(position.z))
                {
                    Debug.LogWarning($"[ProjectileMovement] Invalid position received: {position}");
                    return;
                }

                if (float.IsNaN(velocity.x) || float.IsNaN(velocity.y) || float.IsNaN(velocity.z) ||
                    float.IsInfinity(velocity.x) || float.IsInfinity(velocity.y) || float.IsInfinity(velocity.z))
                {
                    Debug.LogWarning($"[ProjectileMovement] Invalid velocity received: {velocity}");
                    return;
                }

                // Apply validated values
                cachedTransform.position = position;
                cachedTransform.rotation = rotation;

                if (!rb.isKinematic)
                {
                    float maxVelocity = 1000f;
                    if (velocity.magnitude > maxVelocity)
                    {
                        velocity = velocity.normalized * maxVelocity;
                    }
                    rb.linearVelocity = velocity;
                    currentVelocity = velocity;
                }
            }
            catch (System.Exception e)
            {
                Debug.LogError($"[ProjectileMovement] Error applying job results: {e.Message}");
            }
        }

        private void UpdateJobSystemData()
        {
            if (jobSystem == null || projectileIndex < 0) return;

            Vector3 targetPosition = currentTarget != null ? currentTarget.position : cachedTransform.position + cachedTransform.forward * 100f;
            float timeScale = timeline != null ? timeline.timeScale : 1f;

            jobSystem.UpdateProjectileMovementData(
                projectileIndex,
                cachedTransform.position,
                cachedTransform.rotation,
                rb.linearVelocity,
                targetPosition,
                homing && currentTarget != null,
                rotateSpeed,
                bulletSpeed,
                timeScale,
                projectile.Lifetime
            );
        }
        #endregion

        #region Unity Lifecycle
        private void Awake()
        {
            rb = GetComponent<Rigidbody>();
            timeline = GetComponent<Timeline>();
            cachedTransform = transform;
        }

        private void Start()
        {
            lastPosition = cachedTransform.position;

            // Initialize movement state if not already done
            if (!isInitialized && projectile != null)
            {
                Initialize(projectile);
            }
        }

        private void OnDestroy()
        {
            // Clean up job system reference
            if (useJobSystem && jobSystem != null && projectileIndex >= 0)
            {
                // Note: Job system cleanup is handled by ProjectileManager
                projectileIndex = -1;
            }
        }
        #endregion

        #region Debug Methods
        private void OnDrawGizmosSelected()
        {
            if (currentTarget != null)
            {
                // Draw line to target
                Gizmos.color = Color.red;
                Gizmos.DrawLine(transform.position, currentTarget.position);
                Gizmos.DrawWireSphere(currentTarget.position, 1f);

                // Draw predicted position if available
                if (predictedPosition != Vector3.zero)
                {
                    Gizmos.color = Color.yellow;
                    Gizmos.DrawWireSphere(predictedPosition, 0.5f);
                    Gizmos.DrawLine(currentTarget.position, predictedPosition);
                }

                // Draw velocity vector
                Gizmos.color = Color.blue;
                Gizmos.DrawRay(transform.position, currentVelocity.normalized * 5f);

                // Draw arrival distance
                if (homing)
                {
                    Gizmos.color = Color.green;
                    Gizmos.DrawWireSphere(currentTarget.position, arrivalDistance);
                }
            }
        }

        private void OnDrawGizmos()
        {
            // Draw movement trail
            if (Application.isPlaying && lastPosition != Vector3.zero)
            {
                Gizmos.color = Color.cyan;
                Gizmos.DrawLine(lastPosition, transform.position);
            }
        }
        #endregion

        #region Public Configuration Methods
        public void SetAccuracy(float newAccuracy)
        {
            accuracy = Mathf.Clamp01(newAccuracy);
        }

        public void SetSteeringForce(float newSteeringForce)
        {
            steeringForce = Mathf.Max(0f, newSteeringForce);
        }

        public void SetArrivalDistance(float newArrivalDistance)
        {
            arrivalDistance = Mathf.Max(0.1f, newArrivalDistance);
        }

        public void SetRotateSpeed(float newRotateSpeed)
        {
            rotateSpeed = Mathf.Max(0f, newRotateSpeed);
        }

        public void SetUseJobSystem(bool useJob)
        {
            useJobSystem = useJob;

            if (!useJobSystem && jobSystem != null)
            {
                // Switch to direct movement
                projectileIndex = -1;
                jobSystem = null;
            }
        }
        #endregion
    }
}
