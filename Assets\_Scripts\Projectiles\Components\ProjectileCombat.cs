using UnityEngine;
using BTR.Projectiles;

namespace BTR.Projectiles
{
    /// <summary>
    /// Component responsible for projectile combat logic.
    /// This is a placeholder implementation for Phase 1 - logic will be migrated from ProjectileStateBased in Phase 2.
    /// </summary>
    public class ProjectileCombat : MonoBehaviour, IProjectileCombat
    {
        #region Private Fields
        private ProjectileEntity projectile;
        private bool isInitialized = false;

        // Combat configuration
        [SerializeField] private float damageAmount = 10f;
        [SerializeField] private float damageMultiplier = 1f;
        [SerializeField] private LayerMask collisionLayers = -1;
        [SerializeField] private bool enableCollisionDamage = true;
        [SerializeField] private bool requireLineOfSight = false;
        [SerializeField] private bool destroyOnHit = true;
        [SerializeField] private bool canHitMultipleTargets = false;

        // Cached components
        private Collider projectileCollider;
        private Rigidbody projectileRigidbody;

        // Combat state
        private bool hasHitTarget = false;
        private float lastDamageTime = 0f;
        private float damageCooldown = 0.1f; // Prevent multiple damage applications
        private System.Collections.Generic.HashSet<int> hitTargets = new System.Collections.Generic.HashSet<int>();

        // Layer constants (from ProjectileStateBased)
        private const int PLAYER_LAYER = 3;
        private const int ENEMY_LAYER = 6;
        private const int OBSTACLE_LAYER = 8;
        private const int SHIELDABLE_LAYER = 9;
        #endregion

        #region IProjectileCombat Implementation
        public void Initialize(ProjectileEntity projectile)
        {
            this.projectile = projectile;

            // Cache components
            projectileCollider = GetComponent<Collider>();
            projectileRigidbody = GetComponent<Rigidbody>();

            // Initialize state
            hasHitTarget = false;
            lastDamageTime = 0f;
            hitTargets.Clear();

            isInitialized = true;

            Debug.Log($"[ProjectileCombat] Initialized for projectile {projectile.EntityID}");
        }

        public void ApplyDamage(IDamageable target, Vector3 hitPoint)
        {
            if (!isInitialized || target == null || !enableCollisionDamage) return;

            // Check damage cooldown
            if (Time.time - lastDamageTime < damageCooldown) return;

            // Check if we can damage this target
            if (!CanDamage(target)) return;

            // Check if we've already hit this target (for multi-hit prevention)
            int targetId = target.GetInstanceID();
            if (!canHitMultipleTargets && hitTargets.Contains(targetId)) return;

            // Calculate and apply damage
            float finalDamage = CalculateDamage(target);

            try
            {
                target.TakeDamage(finalDamage);

                // Record the hit
                hitTargets.Add(targetId);
                lastDamageTime = Time.time;
                hasHitTarget = true;

                // Trigger hit effects
                OnDamageApplied(target, hitPoint, finalDamage);

                Debug.Log($"[ProjectileCombat] Applied {finalDamage} damage to {target.GetType().Name}");
            }
            catch (System.Exception e)
            {
                Debug.LogError($"[ProjectileCombat] Error applying damage: {e.Message}");
            }
        }

        public float CalculateDamage(IDamageable target)
        {
            if (target == null) return 0f;

            float baseDamage = damageAmount * damageMultiplier;

            // Apply any target-specific modifiers
            // This could be extended for armor, resistances, etc.

            return baseDamage;
        }

        public void HandleCollision(Collision collision)
        {
            if (!isInitialized || collision == null) return;

            GameObject hitObject = collision.gameObject;
            int hitLayer = hitObject.layer;

            Debug.Log($"[ProjectileCombat] Collision with {hitObject.name} on layer {hitLayer}");

            // Check if this layer should be processed
            if (!ShouldProcessLayer(hitLayer)) return;

            // Get collision point
            Vector3 hitPoint = collision.contacts.Length > 0 ? collision.contacts[0].point : transform.position;

            // Process the collision
            ProcessHit(hitObject, hitPoint, true);
        }

        public void HandleTrigger(Collider other)
        {
            if (!isInitialized || other == null) return;

            GameObject hitObject = other.gameObject;
            int hitLayer = hitObject.layer;

            Debug.Log($"[ProjectileCombat] Trigger with {hitObject.name} on layer {hitLayer}");

            // Check if this layer should be processed
            if (!ShouldProcessLayer(hitLayer)) return;

            // Get hit point (approximate)
            Vector3 hitPoint = other.ClosestPoint(transform.position);

            // Process the hit
            ProcessHit(hitObject, hitPoint, false);
        }

        public void SetDamage(float damage)
        {
            damageAmount = damage;
            Debug.Log($"[ProjectileCombat] Damage set to {damage}");
        }

        public float GetDamage()
        {
            return damageAmount;
        }

        public void SetDamageMultiplier(float multiplier)
        {
            damageMultiplier = multiplier;
            Debug.Log($"[ProjectileCombat] Damage multiplier set to {multiplier}");
        }

        public float GetDamageMultiplier()
        {
            return damageMultiplier;
        }

        public bool CanDamage(IDamageable target)
        {
            if (target == null) return false;

            // Check if target is vulnerable
            if (!target.IsVulnerable) return false;

            // Check if we've already hit this target (for single-hit projectiles)
            if (!canHitMultipleTargets && hitTargets.Contains(target.GetInstanceID())) return false;

            // Check projectile ownership vs target type
            if (projectile != null)
            {
                bool isPlayerProjectile = projectile.IsPlayerProjectile;
                bool isEnemyTarget = target.gameObject.layer == ENEMY_LAYER;
                bool isPlayerTarget = target.gameObject.layer == PLAYER_LAYER;

                // Player projectiles should only damage enemies
                if (isPlayerProjectile && !isEnemyTarget) return false;

                // Enemy projectiles should only damage players
                if (!isPlayerProjectile && !isPlayerTarget) return false;
            }

            // Check line of sight if required
            if (requireLineOfSight && !HasLineOfSight(target.gameObject))
            {
                return false;
            }

            return true;
        }

        public void SetCollisionLayers(LayerMask layers)
        {
            collisionLayers = layers;
            Debug.Log($"[ProjectileCombat] Collision layers set to {layers.value}");
        }

        public LayerMask GetCollisionLayers()
        {
            return collisionLayers;
        }
        #endregion

#endregion

        #region Private Methods
        private bool ShouldProcessLayer(int layer)
        {
            // Check if the layer is in our collision mask
            return (collisionLayers.value & (1 << layer)) != 0;
        }

        private void ProcessHit(GameObject hitObject, Vector3 hitPoint, bool isCollision)
        {
            try
            {
                // Handle different layer types
                switch (hitObject.layer)
                {
                    case PLAYER_LAYER:
                        ProcessPlayerHit(hitObject, hitPoint);
                        break;
                    case ENEMY_LAYER:
                        ProcessEnemyHit(hitObject, hitPoint);
                        break;
                    case OBSTACLE_LAYER:
                        ProcessObstacleHit(hitObject, hitPoint);
                        break;
                    case SHIELDABLE_LAYER:
                        ProcessShieldableHit(hitObject, hitPoint);
                        break;
                    default:
                        ProcessGenericHit(hitObject, hitPoint);
                        break;
                }

                // Destroy projectile if configured to do so
                if (destroyOnHit && (!canHitMultipleTargets || hasHitTarget))
                {
                    RequestDestroy();
                }
            }
            catch (System.Exception e)
            {
                Debug.LogError($"[ProjectileCombat] Error processing hit: {e.Message}");
            }
        }

        private void ProcessPlayerHit(GameObject player, Vector3 hitPoint)
        {
            // Only enemy projectiles should damage players
            if (projectile != null && projectile.IsPlayerProjectile) return;

            IDamageable damageable = player.GetComponent<IDamageable>();
            if (damageable != null)
            {
                ApplyDamage(damageable, hitPoint);
            }
        }

        private void ProcessEnemyHit(GameObject enemy, Vector3 hitPoint)
        {
            // Only player projectiles should damage enemies
            if (projectile != null && !projectile.IsPlayerProjectile) return;

            IDamageable damageable = enemy.GetComponent<IDamageable>();
            if (damageable != null)
            {
                ApplyDamage(damageable, hitPoint);
            }
        }

        private void ProcessObstacleHit(GameObject obstacle, Vector3 hitPoint)
        {
            // Obstacles stop all projectiles
            Debug.Log($"[ProjectileCombat] Hit obstacle: {obstacle.name}");

            // Trigger hit effects but no damage
            OnObstacleHit(obstacle, hitPoint);

            // Always destroy on obstacle hit
            RequestDestroy();
        }

        private void ProcessShieldableHit(GameObject shieldable, Vector3 hitPoint)
        {
            // Check if it's a shield or shieldable object
            IDamageable damageable = shieldable.GetComponent<IDamageable>();
            if (damageable != null)
            {
                ApplyDamage(damageable, hitPoint);
            }
        }

        private void ProcessGenericHit(GameObject hitObject, Vector3 hitPoint)
        {
            // Try to find a damageable component
            IDamageable damageable = hitObject.GetComponent<IDamageable>();
            if (damageable != null && CanDamage(damageable))
            {
                ApplyDamage(damageable, hitPoint);
            }
        }

        private bool HasLineOfSight(GameObject target)
        {
            if (target == null) return false;

            Vector3 direction = target.transform.position - transform.position;
            float distance = direction.magnitude;

            // Perform raycast to check for obstacles
            RaycastHit hit;
            if (Physics.Raycast(transform.position, direction.normalized, out hit, distance, collisionLayers))
            {
                // If we hit something other than the target, no line of sight
                return hit.collider.gameObject == target;
            }

            return true; // No obstacles found
        }

        private void RequestDestroy()
        {
            if (projectile != null)
            {
                // Request destruction through the projectile entity
                projectile.RequestDestroy();
            }
            else
            {
                // Fallback: destroy the game object directly
                Destroy(gameObject);
            }
        }

        private void OnDamageApplied(IDamageable target, Vector3 hitPoint, float damage)
        {
            // This can be extended to trigger hit effects, sounds, etc.
            // For now, just log the event
            Debug.Log($"[ProjectileCombat] Damage applied: {damage} to {target.GetType().Name} at {hitPoint}");
        }

        private void OnObstacleHit(GameObject obstacle, Vector3 hitPoint)
        {
            // This can be extended to trigger hit effects for obstacles
            Debug.Log($"[ProjectileCombat] Obstacle hit: {obstacle.name} at {hitPoint}");
        }
        #endregion

        #region Unity Collision Events
        private void OnCollisionEnter(Collision collision)
        {
            HandleCollision(collision);
        }

        private void OnTriggerEnter(Collider other)
        {
            HandleTrigger(other);
        }
        #endregion

        #region Public Configuration Methods
        public void SetEnableCollisionDamage(bool enable)
        {
            enableCollisionDamage = enable;
        }

        public void SetRequireLineOfSight(bool require)
        {
            requireLineOfSight = require;
        }

        public void SetDestroyOnHit(bool destroy)
        {
            destroyOnHit = destroy;
        }

        public void SetCanHitMultipleTargets(bool canHitMultiple)
        {
            canHitMultipleTargets = canHitMultiple;
        }

        public void SetDamageCooldown(float cooldown)
        {
            damageCooldown = Mathf.Max(0f, cooldown);
        }

        public bool HasHitTarget()
        {
            return hasHitTarget;
        }

        public int GetHitTargetCount()
        {
            return hitTargets.Count;
        }

        public void ResetHitTargets()
        {
            hitTargets.Clear();
            hasHitTarget = false;
        }
        #endregion
    }
}
