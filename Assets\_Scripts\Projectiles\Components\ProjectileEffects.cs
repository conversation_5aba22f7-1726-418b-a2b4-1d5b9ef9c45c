using UnityEngine;
using FMODUnity;
using BTR.Projectiles;

namespace BTR.Projectiles
{
    /// <summary>
    /// Component responsible for projectile visual and audio effects.
    /// Migrated from ProjectileStateBased visual effects and material management system.
    /// </summary>
    public class ProjectileEffects : MonoBehaviour, IProjectileEffects
    {
        #region Private Fields
        private ProjectileEntity projectile;
        private bool isInitialized = false;

        // Visual configuration
        [SerializeField] private bool trailEnabled = true;
        [SerializeField] private bool particlesEnabled = true;
        [SerializeField] private Color currentColor = Color.white;
        [SerializeField] private float currentScale = 1f;
        [SerializeField] private bool useColorOverride = false;
        [SerializeField] private bool useScaleOverride = false;

        // Audio configuration
        [SerializeField] private EventReference hitSoundEvent;
        [SerializeField] private EventReference destroySoundEvent;
        [SerializeField] private bool enableAudio = true;

        // Cached components
        private TrailRenderer trailRenderer;
        private ParticleSystem[] particleSystems;
        private Renderer modelRenderer;
        private LineRenderer lineRenderer;
        private Transform cachedTransform;

        // Material management
        private Material[] originalMaterials;
        private Material[] currentMaterials;
        private MaterialPropertyBlock propertyBlock;

        // Effect state
        private bool effectsActive = true;
        private Vector3 lastVelocity = Vector3.zero;
        private float velocityMagnitude = 0f;

        // Performance optimization
        private float lastEffectUpdate = 0f;
        private float effectUpdateInterval = 0.016f; // ~60 FPS
        #endregion

        #region IProjectileEffects Implementation
        public void Initialize(ProjectileEntity projectile)
        {
            this.projectile = projectile;

            // Cache components
            cachedTransform = transform;
            CacheEffectComponents();

            // Initialize material management
            InitializeMaterials();

            // Initialize property block for material modifications
            if (propertyBlock == null)
            {
                propertyBlock = new MaterialPropertyBlock();
            }

            // Apply initial settings
            ApplyInitialEffectSettings();

            isInitialized = true;

            Debug.Log($"[ProjectileEffects] Initialized for projectile {projectile.EntityID}");
        }

        public void OnSpawn()
        {
            if (!isInitialized) return;

            // Enable all effects
            effectsActive = true;

            // Start trail if enabled
            if (trailEnabled && trailRenderer != null)
            {
                trailRenderer.enabled = true;
                trailRenderer.Clear(); // Clear any previous trail
            }

            // Start particles if enabled
            if (particlesEnabled && particleSystems != null)
            {
                foreach (var ps in particleSystems)
                {
                    if (ps != null && !ps.isPlaying)
                    {
                        ps.Play();
                    }
                }
            }

            // Apply current color and scale
            ApplyVisualSettings();

            Debug.Log($"[ProjectileEffects] Spawn effects triggered");
        }

        public void OnUpdate()
        {
            if (!isInitialized || !effectsActive) return;

            // Performance optimization: limit update frequency
            if (Time.time - lastEffectUpdate < effectUpdateInterval) return;
            lastEffectUpdate = Time.time;

            // Update velocity-based effects
            UpdateVelocityBasedEffects();

            // Update particle systems based on movement
            UpdateParticleEffects();
        }

        public void OnHit(Vector3 hitPoint, Vector3 normal)
        {
            if (!isInitialized) return;

            // Play hit sound
            if (enableAudio && !hitSoundEvent.IsNull)
            {
                RuntimeManager.PlayOneShot(hitSoundEvent, hitPoint);
            }

            // Stop trail immediately on hit
            if (trailRenderer != null)
            {
                trailRenderer.enabled = false;
            }

            // Stop particles on hit
            if (particleSystems != null)
            {
                foreach (var ps in particleSystems)
                {
                    if (ps != null && ps.isPlaying)
                    {
                        ps.Stop();
                    }
                }
            }

            Debug.Log($"[ProjectileEffects] Hit effects at {hitPoint}");
        }

        public void OnDestroy()
        {
            if (!isInitialized) return;

            // Play destroy sound
            if (enableAudio && !destroySoundEvent.IsNull)
            {
                RuntimeManager.PlayOneShot(destroySoundEvent, cachedTransform.position);
            }

            // Cleanup all effects
            CleanupEffects();

            Debug.Log($"[ProjectileEffects] Destroy effects triggered");
        }

        public void OnVelocityChanged(Vector3 velocity)
        {
            if (!isInitialized) return;

            lastVelocity = velocity;
            velocityMagnitude = velocity.magnitude;

            // Update trail width based on velocity
            UpdateTrailBasedOnVelocity();

            // Update particle emission based on velocity
            UpdateParticleEmissionBasedOnVelocity();
        }

        public void SetTrailEnabled(bool enabled)
        {
            trailEnabled = enabled;
            if (trailRenderer != null)
            {
                trailRenderer.enabled = enabled;
            }
            Debug.Log($"[ProjectileEffects] Trail {(enabled ? "enabled" : "disabled")}");
        }

        public bool IsTrailEnabled()
        {
            return trailEnabled;
        }

        public void SetTrailMaterial(Material material)
        {
            if (trailRenderer != null && material != null)
            {
                trailRenderer.material = material;
                Debug.Log($"[ProjectileEffects] Trail material set to {material.name}");
            }
        }

        public void SetParticlesEnabled(bool enabled)
        {
            particlesEnabled = enabled;
            if (particleSystems != null)
            {
                foreach (var ps in particleSystems)
                {
                    if (ps != null)
                    {
                        if (enabled)
                            ps.Play();
                        else
                            ps.Stop();
                    }
                }
            }
            Debug.Log($"[ProjectileEffects] Particles {(enabled ? "enabled" : "disabled")}");
        }

        public bool IsParticlesEnabled()
        {
            return particlesEnabled;
        }

        public void SetColor(Color color)
        {
            currentColor = color;
            useColorOverride = true;

            // Apply color to all materials
            ApplyColorToMaterials(color);

            // Apply color to trail
            if (trailRenderer != null)
            {
                trailRenderer.startColor = color;
                trailRenderer.endColor = new Color(color.r, color.g, color.b, 0f);
            }

            // Apply color to particles
            ApplyColorToParticles(color);

            Debug.Log($"[ProjectileEffects] Color set to {color}");
        }

        public Color GetColor()
        {
            return currentColor;
        }

        public void SetScale(float scale)
        {
            currentScale = scale;
            useScaleOverride = true;

            // Apply scale to transform
            cachedTransform.localScale = Vector3.one * scale;

            // Apply scale to trail width
            if (trailRenderer != null)
            {
                trailRenderer.widthMultiplier = scale;
            }

            // Apply scale to particle systems
            ApplyScaleToParticles(scale);

            Debug.Log($"[ProjectileEffects] Scale set to {scale}");
        }

        public float GetScale()
        {
            return currentScale;
        }

        public void CleanupEffects()
        {
            effectsActive = false;

            // Clear trail
            if (trailRenderer != null)
            {
                trailRenderer.enabled = false;
                trailRenderer.Clear();
            }

            // Stop and clear particles
            if (particleSystems != null)
            {
                foreach (var ps in particleSystems)
                {
                    if (ps != null)
                    {
                        ps.Stop();
                        ps.Clear();
                    }
                }
            }

            // Clear line renderer if present
            if (lineRenderer != null)
            {
                lineRenderer.enabled = false;
            }

            Debug.Log($"[ProjectileEffects] Effects cleaned up");
        }

        public void ResetForPool()
        {
            // Reset state
            currentColor = Color.white;
            currentScale = 1f;
            trailEnabled = true;
            particlesEnabled = true;
            useColorOverride = false;
            useScaleOverride = false;
            effectsActive = true;
            lastVelocity = Vector3.zero;
            velocityMagnitude = 0f;
            lastEffectUpdate = 0f;

            // Cleanup current effects
            CleanupEffects();

            // Reset materials to original
            RestoreOriginalMaterials();

            // Reset transform scale
            if (cachedTransform != null)
            {
                cachedTransform.localScale = Vector3.one;
            }

            Debug.Log($"[ProjectileEffects] Reset for pool");
        }

        public void SetAudioEnabled(bool enabled)
        {
            enableAudio = enabled;
        }

        public bool IsAudioEnabled()
        {
            return enableAudio;
        }

        public void SetHitSoundEvent(EventReference soundEvent)
        {
            hitSoundEvent = soundEvent;
        }

        public void SetDestroySoundEvent(EventReference soundEvent)
        {
            destroySoundEvent = soundEvent;
        }
        #endregion

        #region Private Methods
        private void CacheEffectComponents()
        {
            trailRenderer = GetComponent<TrailRenderer>();
            particleSystems = GetComponentsInChildren<ParticleSystem>();
            lineRenderer = GetComponent<LineRenderer>();

            // Find the Projectile Model child object's renderer
            var projectileModel = cachedTransform.Find("Projectile Model");
            if (projectileModel != null)
            {
                modelRenderer = projectileModel.GetComponent<Renderer>();
            }
            else
            {
                // Fallback to any renderer on this object
                modelRenderer = GetComponent<Renderer>();
            }
        }

        private void InitializeMaterials()
        {
            if (modelRenderer != null)
            {
                originalMaterials = modelRenderer.materials;
                currentMaterials = new Material[originalMaterials.Length];

                // Create instances of materials for modification
                for (int i = 0; i < originalMaterials.Length; i++)
                {
                    if (originalMaterials[i] != null)
                    {
                        currentMaterials[i] = new Material(originalMaterials[i]);
                    }
                }

                modelRenderer.materials = currentMaterials;
            }
        }

        private void ApplyInitialEffectSettings()
        {
            // Apply initial trail settings
            if (trailRenderer != null)
            {
                trailRenderer.enabled = trailEnabled;
            }

            // Apply initial particle settings
            if (particleSystems != null)
            {
                foreach (var ps in particleSystems)
                {
                    if (ps != null)
                    {
                        if (particlesEnabled)
                            ps.Play();
                        else
                            ps.Stop();
                    }
                }
            }
        }

        private void ApplyVisualSettings()
        {
            if (useColorOverride)
            {
                ApplyColorToMaterials(currentColor);
                ApplyColorToParticles(currentColor);

                if (trailRenderer != null)
                {
                    trailRenderer.startColor = currentColor;
                    trailRenderer.endColor = new Color(currentColor.r, currentColor.g, currentColor.b, 0f);
                }
            }

            if (useScaleOverride)
            {
                cachedTransform.localScale = Vector3.one * currentScale;
                ApplyScaleToParticles(currentScale);

                if (trailRenderer != null)
                {
                    trailRenderer.widthMultiplier = currentScale;
                }
            }
        }

        private void UpdateVelocityBasedEffects()
        {
            // Update trail based on velocity
            UpdateTrailBasedOnVelocity();

            // Update particle emission based on velocity
            UpdateParticleEmissionBasedOnVelocity();
        }

        private void UpdateParticleEffects()
        {
            if (particleSystems == null) return;

            foreach (var ps in particleSystems)
            {
                if (ps != null && ps.isPlaying)
                {
                    // Update particle system properties based on current state
                    var main = ps.main;

                    // Adjust emission rate based on velocity
                    var emission = ps.emission;
                    float baseRate = 10f; // Base emission rate
                    float velocityFactor = Mathf.Clamp01(velocityMagnitude / 100f); // Normalize velocity
                    emission.rateOverTime = baseRate * (1f + velocityFactor);
                }
            }
        }

        private void UpdateTrailBasedOnVelocity()
        {
            if (trailRenderer == null) return;

            // Adjust trail width based on velocity
            float baseWidth = 0.1f;
            float velocityFactor = Mathf.Clamp01(velocityMagnitude / 50f);
            float dynamicWidth = baseWidth * (1f + velocityFactor * 0.5f);

            if (useScaleOverride)
            {
                dynamicWidth *= currentScale;
            }

            trailRenderer.widthMultiplier = dynamicWidth;
        }

        private void UpdateParticleEmissionBasedOnVelocity()
        {
            if (particleSystems == null) return;

            foreach (var ps in particleSystems)
            {
                if (ps != null && ps.isPlaying)
                {
                    var emission = ps.emission;
                    float baseRate = 10f;
                    float velocityFactor = Mathf.Clamp01(velocityMagnitude / 100f);
                    emission.rateOverTime = baseRate * (1f + velocityFactor);
                }
            }
        }

        private void ApplyColorToMaterials(Color color)
        {
            if (modelRenderer == null || currentMaterials == null) return;

            try
            {
                // Use MaterialPropertyBlock for better performance
                propertyBlock.SetColor("_Color", color);
                propertyBlock.SetColor("_BaseColor", color); // URP
                propertyBlock.SetColor("_MainColor", color); // Custom shaders

                modelRenderer.SetPropertyBlock(propertyBlock);
            }
            catch (System.Exception e)
            {
                Debug.LogWarning($"[ProjectileEffects] Error applying color to materials: {e.Message}");
            }
        }

        private void ApplyColorToParticles(Color color)
        {
            if (particleSystems == null) return;

            foreach (var ps in particleSystems)
            {
                if (ps != null)
                {
                    var main = ps.main;
                    main.startColor = color;
                }
            }
        }

        private void ApplyScaleToParticles(float scale)
        {
            if (particleSystems == null) return;

            foreach (var ps in particleSystems)
            {
                if (ps != null)
                {
                    var main = ps.main;
                    main.startSizeMultiplier = scale;
                }
            }
        }

        private void RestoreOriginalMaterials()
        {
            if (modelRenderer != null && originalMaterials != null)
            {
                // Destroy current material instances
                if (currentMaterials != null)
                {
                    for (int i = 0; i < currentMaterials.Length; i++)
                    {
                        if (currentMaterials[i] != null)
                        {
                            DestroyImmediate(currentMaterials[i]);
                        }
                    }
                }

                // Restore original materials
                modelRenderer.materials = originalMaterials;

                // Clear property block
                if (propertyBlock != null)
                {
                    modelRenderer.SetPropertyBlock(null);
                }
            }
        }
        #endregion

        #region Unity Lifecycle
        private void Awake()
        {
            cachedTransform = transform;
            CacheEffectComponents();
        }

        private void Start()
        {
            // Initialize materials if not already done
            if (originalMaterials == null)
            {
                InitializeMaterials();
            }
        }

        private void OnDestroy()
        {
            // Cleanup material instances
            if (currentMaterials != null)
            {
                for (int i = 0; i < currentMaterials.Length; i++)
                {
                    if (currentMaterials[i] != null)
                    {
                        DestroyImmediate(currentMaterials[i]);
                    }
                }
            }
        }
        #endregion

        #region Public Configuration Methods
        public void SetEffectUpdateInterval(float interval)
        {
            effectUpdateInterval = Mathf.Max(0.001f, interval);
        }

        public void SetEffectsActive(bool active)
        {
            effectsActive = active;

            if (!active)
            {
                CleanupEffects();
            }
        }

        public bool AreEffectsActive()
        {
            return effectsActive;
        }
        #endregion
    }
}
